<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .vulnerability-high { background-color: #f8d7da; }
        .vulnerability-medium { background-color: #fff3cd; }
        .vulnerability-low { background-color: #d1ecf1; }
        .similarity-score { font-weight: bold; }
        .request-method { 
            font-size: 0.8em; 
            padding: 2px 6px; 
            border-radius: 3px; 
        }
        .method-get { background-color: #28a745; color: white; }
        .method-post { background-color: #007bff; color: white; }
        .method-put { background-color: #ffc107; color: black; }
        .method-delete { background-color: #dc3545; color: white; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt"></i> Authentication Tester
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="proxy-status">
                    <i class="fas fa-circle text-success"></i> Proxy: Active
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Profile Management -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-cog"></i> Test Profiles</h5>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#profileModal">
                            <i class="fas fa-plus"></i> New Profile
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="profiles-list">
                            <!-- Profiles will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Profile Info -->
        <div class="row mb-4" id="active-profile-section" style="display: none;">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-shield"></i> Active Profile: <span id="active-profile-name"></span></h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Accounts</h6>
                                <div id="accounts-list">
                                    <!-- Accounts will be loaded here -->
                                </div>
                                <button class="btn btn-sm btn-outline-primary mt-2" data-bs-toggle="modal" data-bs-target="#accountModal">
                                    <i class="fas fa-plus"></i> Add Account
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h6>Settings</h6>
                                <div id="profile-settings">
                                    <!-- Settings will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bug"></i> Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="results-table">
                                <thead>
                                    <tr>
                                        <th>Method</th>
                                        <th>URL</th>
                                        <th>Account</th>
                                        <th>Similarity</th>
                                        <th>Status</th>
                                        <th>Type</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="results-tbody">
                                    <!-- Results will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                        <nav>
                            <ul class="pagination justify-content-center" id="results-pagination">
                                <!-- Pagination will be loaded here -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <div class="modal fade" id="profileModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create Test Profile</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="profile-form">
                        <div class="mb-3">
                            <label for="profile-name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="profile-name" required>
                        </div>
                        <div class="mb-3">
                            <label for="profile-description" class="form-label">Description</label>
                            <textarea class="form-control" id="profile-description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="target-scope" class="form-label">Target Scope (one per line)</label>
                            <textarea class="form-control" id="target-scope" rows="3" placeholder="https://example.com/api/&#10;https://app.example.com/"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="similarity-threshold" class="form-label">Similarity Threshold</label>
                            <input type="number" class="form-control" id="similarity-threshold" min="0" max="1" step="0.1" value="0.8">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="createProfile()">Create</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Modal -->
    <div class="modal fade" id="accountModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Account</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="account-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account-name" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="account-name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account-type" class="form-label">Type</label>
                                    <select class="form-control" id="account-type" required>
                                        <option value="baseline">Baseline (A-type)</option>
                                        <option value="same">Same Privilege (B-type)</option>
                                        <option value="low">Low Privilege (C-type)</option>
                                        <option value="custom">Custom (D-type)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="account-priority" class="form-label">Priority</label>
                            <input type="number" class="form-control" id="account-priority" placeholder="Auto-assigned based on type">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Headers</label>
                            <div id="headers-container">
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control" placeholder="Header Name" name="header-name">
                                    <input type="text" class="form-control" placeholder="Header Value" name="header-value">
                                    <button class="btn btn-outline-danger" type="button" onclick="removeHeaderRow(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addHeaderRow()">
                                <i class="fas fa-plus"></i> Add Header
                            </button>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Cookies</label>
                            <div id="cookies-container">
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control" placeholder="Cookie Name" name="cookie-name">
                                    <input type="text" class="form-control" placeholder="Cookie Value" name="cookie-value">
                                    <button class="btn btn-outline-danger" type="button" onclick="removeCookieRow(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addCookieRow()">
                                <i class="fas fa-plus"></i> Add Cookie
                            </button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="createAccount()">Add Account</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
