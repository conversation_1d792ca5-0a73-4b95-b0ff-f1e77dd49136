let currentProfileId = null;
let currentPage = 0;
const pageSize = 20;

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadProfiles();
    loadResults();
    
    // Refresh data every 30 seconds
    setInterval(() => {
        if (currentProfileId) {
            loadResults();
        }
    }, 30000);
});

// Load all profiles
async function loadProfiles() {
    try {
        const response = await fetch('/api/profiles');
        const profiles = await response.json();
        
        const profilesList = document.getElementById('profiles-list');
        profilesList.innerHTML = '';
        
        if (profiles.length === 0) {
            profilesList.innerHTML = '<p class="text-muted">No profiles created yet.</p>';
            return;
        }
        
        profiles.forEach(profile => {
            const profileCard = createProfileCard(profile);
            profilesList.appendChild(profileCard);
            
            if (profile.active) {
                currentProfileId = profile.id;
                loadActiveProfile(profile);
            }
        });
    } catch (error) {
        console.error('Failed to load profiles:', error);
        showAlert('Failed to load profiles', 'danger');
    }
}

// Create profile card element
function createProfileCard(profile) {
    const div = document.createElement('div');
    div.className = `card mb-2 ${profile.active ? 'border-primary' : ''}`;
    
    div.innerHTML = `
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="card-title mb-1">
                        ${profile.name}
                        ${profile.active ? '<span class="badge bg-primary ms-2">Active</span>' : ''}
                    </h6>
                    <p class="card-text text-muted mb-1">${profile.description || 'No description'}</p>
                    <small class="text-muted">
                        ${profile.accounts ? profile.accounts.length : 0} accounts | 
                        ${profile.target_scope ? profile.target_scope.length : 0} targets
                    </small>
                </div>
                <div class="btn-group">
                    ${!profile.active ? `<button class="btn btn-sm btn-outline-primary" onclick="activateProfile(${profile.id})">Activate</button>` : ''}
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteProfile(${profile.id})">Delete</button>
                </div>
            </div>
        </div>
    `;
    
    return div;
}

// Load active profile details
function loadActiveProfile(profile) {
    document.getElementById('active-profile-section').style.display = 'block';
    document.getElementById('active-profile-name').textContent = profile.name;
    
    // Load accounts
    const accountsList = document.getElementById('accounts-list');
    accountsList.innerHTML = '';
    
    if (profile.accounts && profile.accounts.length > 0) {
        profile.accounts.forEach(account => {
            const accountBadge = createAccountBadge(account);
            accountsList.appendChild(accountBadge);
        });
    } else {
        accountsList.innerHTML = '<p class="text-muted small">No accounts configured</p>';
    }
    
    // Load settings
    const settingsDiv = document.getElementById('profile-settings');
    settingsDiv.innerHTML = `
        <div class="small">
            <div><strong>Similarity Threshold:</strong> ${profile.settings?.similarity_threshold || 0.8}</div>
            <div><strong>Replay Enabled:</strong> ${profile.settings?.enable_replay ? 'Yes' : 'No'}</div>
            <div><strong>Max Concurrency:</strong> ${profile.settings?.max_concurrency || 5}</div>
        </div>
    `;
}

// Create account badge
function createAccountBadge(account) {
    const span = document.createElement('span');
    
    let badgeClass = 'bg-secondary';
    switch (account.type) {
        case 'baseline': badgeClass = 'bg-primary'; break;
        case 'same': badgeClass = 'bg-success'; break;
        case 'low': badgeClass = 'bg-warning'; break;
        case 'custom': badgeClass = 'bg-info'; break;
    }
    
    span.className = `badge ${badgeClass} me-1 mb-1`;
    span.textContent = `${account.name} (${account.type})`;
    
    return span;
}

// Load test results
async function loadResults() {
    if (!currentProfileId) return;
    
    try {
        const response = await fetch(`/api/profiles/${currentProfileId}/results?limit=${pageSize}&offset=${currentPage * pageSize}`);
        const results = await response.json();
        
        const tbody = document.getElementById('results-tbody');
        tbody.innerHTML = '';
        
        if (results.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No test results yet</td></tr>';
            return;
        }
        
        results.forEach(result => {
            const row = createResultRow(result);
            tbody.appendChild(row);
        });
    } catch (error) {
        console.error('Failed to load results:', error);
        showAlert('Failed to load test results', 'danger');
    }
}

// Create result row
function createResultRow(result) {
    const tr = document.createElement('tr');
    
    if (result.is_vulnerable) {
        tr.className = result.vuln_type === 'vertical' ? 'vulnerability-high' : 'vulnerability-medium';
    }
    
    const methodClass = `method-${result.request?.method?.toLowerCase() || 'get'}`;
    const similarityClass = result.similarity > 0.8 ? 'text-danger' : result.similarity > 0.5 ? 'text-warning' : 'text-success';
    
    tr.innerHTML = `
        <td><span class="request-method ${methodClass}">${result.request?.method || 'GET'}</span></td>
        <td><small>${truncateUrl(result.request?.url || '')}</small></td>
        <td>${result.test_resp?.account?.name || 'Unknown'}</td>
        <td><span class="similarity-score ${similarityClass}">${(result.similarity * 100).toFixed(1)}%</span></td>
        <td>
            ${result.is_vulnerable ? 
                `<span class="badge bg-danger">Vulnerable</span>` : 
                `<span class="badge bg-success">Safe</span>`
            }
        </td>
        <td>${result.vuln_type || '-'}</td>
        <td>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="viewDetails(${result.id})" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-outline-warning" onclick="toggleVulnerable(${result.id}, ${!result.is_vulnerable})" title="Toggle Status">
                    <i class="fas fa-flag"></i>
                </button>
                <button class="btn btn-outline-info" onclick="addNote(${result.id})" title="Add Note">
                    <i class="fas fa-comment"></i>
                </button>
            </div>
        </td>
    `;
    
    return tr;
}

// Utility functions
function truncateUrl(url) {
    if (url.length > 50) {
        return url.substring(0, 47) + '...';
    }
    return url;
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Profile management functions
async function createProfile() {
    const name = document.getElementById('profile-name').value;
    const description = document.getElementById('profile-description').value;
    const targetScope = document.getElementById('target-scope').value.split('\n').filter(s => s.trim());
    const similarityThreshold = parseFloat(document.getElementById('similarity-threshold').value);
    
    if (!name.trim()) {
        showAlert('Profile name is required', 'danger');
        return;
    }
    
    const profile = {
        name: name.trim(),
        description: description.trim(),
        target_scope: targetScope,
        settings: {
            similarity_threshold: similarityThreshold,
            enable_replay: true,
            max_concurrency: 5,
            request_timeout: 30
        }
    };
    
    try {
        const response = await fetch('/api/profiles', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(profile)
        });
        
        if (response.ok) {
            showAlert('Profile created successfully', 'success');
            document.getElementById('profile-form').reset();
            bootstrap.Modal.getInstance(document.getElementById('profileModal')).hide();
            loadProfiles();
        } else {
            const error = await response.json();
            showAlert(`Failed to create profile: ${error.error}`, 'danger');
        }
    } catch (error) {
        console.error('Failed to create profile:', error);
        showAlert('Failed to create profile', 'danger');
    }
}

async function activateProfile(profileId) {
    try {
        const response = await fetch(`/api/profiles/${profileId}/activate`, {
            method: 'PUT'
        });
        
        if (response.ok) {
            showAlert('Profile activated', 'success');
            loadProfiles();
            loadResults();
        } else {
            const error = await response.json();
            showAlert(`Failed to activate profile: ${error.error}`, 'danger');
        }
    } catch (error) {
        console.error('Failed to activate profile:', error);
        showAlert('Failed to activate profile', 'danger');
    }
}

async function deleteProfile(profileId) {
    if (!confirm('Are you sure you want to delete this profile?')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/profiles/${profileId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showAlert('Profile deleted', 'success');
            if (currentProfileId === profileId) {
                currentProfileId = null;
                document.getElementById('active-profile-section').style.display = 'none';
            }
            loadProfiles();
            loadResults();
        } else {
            const error = await response.json();
            showAlert(`Failed to delete profile: ${error.error}`, 'danger');
        }
    } catch (error) {
        console.error('Failed to delete profile:', error);
        showAlert('Failed to delete profile', 'danger');
    }
}

// Account management functions
function addHeaderRow() {
    const container = document.getElementById('headers-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" placeholder="Header Name" name="header-name">
        <input type="text" class="form-control" placeholder="Header Value" name="header-value">
        <button class="btn btn-outline-danger" type="button" onclick="removeHeaderRow(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeHeaderRow(button) {
    button.parentElement.remove();
}

function addCookieRow() {
    const container = document.getElementById('cookies-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" placeholder="Cookie Name" name="cookie-name">
        <input type="text" class="form-control" placeholder="Cookie Value" name="cookie-value">
        <button class="btn btn-outline-danger" type="button" onclick="removeCookieRow(this)">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeCookieRow(button) {
    button.parentElement.remove();
}

async function createAccount() {
    if (!currentProfileId) {
        showAlert('No active profile selected', 'danger');
        return;
    }

    const name = document.getElementById('account-name').value;
    const type = document.getElementById('account-type').value;
    const priority = document.getElementById('account-priority').value;

    if (!name.trim()) {
        showAlert('Account name is required', 'danger');
        return;
    }

    // Collect headers
    const headers = [];
    const headerNames = document.querySelectorAll('input[name="header-name"]');
    const headerValues = document.querySelectorAll('input[name="header-value"]');

    for (let i = 0; i < headerNames.length; i++) {
        const name = headerNames[i].value.trim();
        const value = headerValues[i].value.trim();
        if (name && value) {
            headers.push({ name, value });
        }
    }

    // Collect cookies
    const cookies = [];
    const cookieNames = document.querySelectorAll('input[name="cookie-name"]');
    const cookieValues = document.querySelectorAll('input[name="cookie-value"]');

    for (let i = 0; i < cookieNames.length; i++) {
        const name = cookieNames[i].value.trim();
        const value = cookieValues[i].value.trim();
        if (name && value) {
            cookies.push({ name, value });
        }
    }

    const account = {
        name: name.trim(),
        type: type,
        priority: priority ? parseInt(priority) : undefined,
        headers: headers,
        cookies: cookies,
        params: []
    };

    try {
        const response = await fetch(`/api/profiles/${currentProfileId}/accounts`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(account)
        });

        if (response.ok) {
            showAlert('Account created successfully', 'success');
            document.getElementById('account-form').reset();
            bootstrap.Modal.getInstance(document.getElementById('accountModal')).hide();
            loadProfiles();
        } else {
            const error = await response.json();
            showAlert(`Failed to create account: ${error.error}`, 'danger');
        }
    } catch (error) {
        console.error('Failed to create account:', error);
        showAlert('Failed to create account', 'danger');
    }
}

// Result management functions
async function toggleVulnerable(resultId, isVulnerable) {
    try {
        const response = await fetch(`/api/results/${resultId}/mark-vulnerable`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ is_vulnerable: isVulnerable })
        });

        if (response.ok) {
            showAlert('Result updated', 'success');
            loadResults();
        } else {
            const error = await response.json();
            showAlert(`Failed to update result: ${error.error}`, 'danger');
        }
    } catch (error) {
        console.error('Failed to update result:', error);
        showAlert('Failed to update result', 'danger');
    }
}

async function addNote(resultId) {
    const note = prompt('Enter a note for this result:');
    if (!note) return;

    try {
        const response = await fetch(`/api/results/${resultId}/add-note`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ note: note })
        });

        if (response.ok) {
            showAlert('Note added', 'success');
            loadResults();
        } else {
            const error = await response.json();
            showAlert(`Failed to add note: ${error.error}`, 'danger');
        }
    } catch (error) {
        console.error('Failed to add note:', error);
        showAlert('Failed to add note', 'danger');
    }
}

function viewDetails(resultId) {
    // This would open a modal with detailed request/response information
    // For now, just show an alert
    showAlert('Detail view not implemented yet', 'info');
}
