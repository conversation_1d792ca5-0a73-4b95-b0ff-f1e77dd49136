package main

import (
	"fmt"
	"os"

	"auth-tester/internal/config"
	"auth-tester/internal/proxy"
	"auth-tester/internal/storage"
	"auth-tester/internal/web"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
)

var (
	configFile string
	port       int
	proxyPort  int
	verbose    bool
)

func main() {
	var rootCmd = &cobra.Command{
		Use:   "auth-tester",
		Short: "Authentication Testing Framework",
		Long: `A comprehensive authentication testing framework inspired by Burp Suite's 
authentication testing capabilities. Supports privilege escalation testing,
automated request replay, and response comparison.`,
		Run: runServer,
	}

	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "config.yaml", "config file")
	rootCmd.PersistentFlags().IntVarP(&port, "port", "p", 8080, "web interface port")
	rootCmd.PersistentFlags().IntVarP(&proxyPort, "proxy-port", "P", 8081, "proxy server port")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "verbose logging")

	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func runServer(cmd *cobra.Command, args []string) {
	// Setup logging
	if verbose {
		logrus.SetLevel(logrus.DebugLevel)
	}

	logrus.Info("Starting Authentication Tester...")

	// Load configuration
	cfg, err := config.Load(configFile)
	if err != nil {
		logrus.Fatalf("Failed to load config: %v", err)
	}

	// Initialize database
	db, err := storage.Initialize(cfg.Database.Path)
	if err != nil {
		logrus.Fatalf("Failed to initialize database: %v", err)
	}

	// Start proxy server
	proxyServer := proxy.NewServer(proxyPort, db)
	go func() {
		if err := proxyServer.Start(); err != nil {
			logrus.Fatalf("Failed to start proxy server: %v", err)
		}
	}()

	// Start web interface
	webServer := web.NewServer(port, db, proxyServer)
	if err := webServer.Start(); err != nil {
		logrus.Fatalf("Failed to start web server: %v", err)
	}
}
