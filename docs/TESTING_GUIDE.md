# Authentication Testing Guide

This guide provides comprehensive instructions for using the Authentication Tester to identify privilege escalation vulnerabilities.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Testing Methodology](#testing-methodology)
3. [Account Configuration](#account-configuration)
4. [Common Scenarios](#common-scenarios)
5. [Result Analysis](#result-analysis)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)

## Quick Start

### 1. Installation and Setup

```bash
# Clone and setup
git clone <repository-url>
cd auth-tester
./setup.sh

# Or use Docker
docker build -t auth-tester .
docker run -p 8080:8080 -p 8081:8081 auth-tester
```

### 2. Basic Configuration

1. **Start the application**:
   ```bash
   ./auth-tester --verbose
   ```

2. **Configure browser proxy**:
   - Set HTTP proxy to `localhost:8081`
   - For Chrome: `google-chrome --proxy-server="localhost:8081"`

3. **Access web interface**:
   - Open `http://localhost:8080`

## Testing Methodology

### Account Classification System

The tool uses a four-tier account system based on the Burp Suite methodology:

| Type | Description | Priority | Use Case |
|------|-------------|----------|----------|
| **A-type (Baseline)** | Reference account with full privileges | -1 (Last) | Comparison baseline |
| **B-type (Same)** | Account with same privilege level | 50 | Horizontal privilege testing |
| **C-type (Low)** | Account with lower privileges | 101 (First) | Vertical privilege testing |
| **D-type (Custom)** | Custom role account | 1-100 | Complex scenarios |

### Execution Order

Requests are executed in priority order to handle destructive operations:

1. **C-type accounts** (highest priority) - Execute first
2. **D-type accounts** (configurable priority)
3. **B-type accounts** (medium priority)
4. **A-type accounts** (lowest priority) - Execute last as baseline

## Account Configuration

### 1. Creating Test Profiles

```json
{
  "name": "E-commerce API Test",
  "description": "Testing privilege escalation in e-commerce API",
  "target_scope": [
    "https://api.shop.com/",
    "https://admin.shop.com/api/"
  ],
  "settings": {
    "similarity_threshold": 0.8,
    "enable_replay": true,
    "max_concurrency": 5
  }
}
```

### 2. Account Examples

#### Admin Account (A-type - Baseline)
```json
{
  "name": "Admin User",
  "type": "baseline",
  "headers": [
    {"name": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."},
    {"name": "X-User-Role", "value": "admin"}
  ],
  "cookies": [
    {"name": "sessionid", "value": "admin_session_12345"}
  ]
}
```

#### Regular User (B-type - Same Privilege)
```json
{
  "name": "Regular User",
  "type": "same",
  "headers": [
    {"name": "Authorization", "value": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."},
    {"name": "X-User-Role", "value": "user"}
  ],
  "cookies": [
    {"name": "sessionid", "value": "user_session_67890"}
  ]
}
```

#### Guest User (C-type - Low Privilege)
```json
{
  "name": "Guest User",
  "type": "low",
  "headers": [
    {"name": "Authorization", "value": "Bearer guest_token_here"}
  ]
}
```

## Common Scenarios

### 1. REST API Testing

**Target Scope**:
```
https://api.example.com/v1/
https://api.example.com/v2/
```

**Common Endpoints to Test**:
- `GET /api/users/{id}` - User profile access
- `PUT /api/users/{id}` - User profile modification
- `DELETE /api/users/{id}` - User deletion
- `GET /api/admin/users` - Admin user listing
- `POST /api/orders` - Order creation
- `GET /api/orders/{id}` - Order access

### 2. Web Application Testing

**Target Scope**:
```
https://app.example.com/dashboard
https://app.example.com/admin
https://app.example.com/api
```

**Account Setup**:
- Admin with full access
- Manager with limited admin access
- Regular user with basic access
- Guest with read-only access

### 3. Multi-tenant Application

**Account Configuration**:
```json
{
  "name": "Tenant A Admin",
  "type": "baseline",
  "headers": [
    {"name": "Authorization", "value": "Bearer tenant_a_admin_token"},
    {"name": "X-Tenant-ID", "value": "tenant-a"}
  ]
},
{
  "name": "Tenant B Admin",
  "type": "same",
  "headers": [
    {"name": "Authorization", "value": "Bearer tenant_b_admin_token"},
    {"name": "X-Tenant-ID", "value": "tenant-b"}
  ]
}
```

## Result Analysis

### Understanding Similarity Scores

| Score Range | Interpretation | Action |
|-------------|----------------|---------|
| 90-100% | Identical responses | Likely vulnerable |
| 80-89% | Very similar | Review manually |
| 60-79% | Somewhat similar | Investigate |
| 0-59% | Different responses | Likely safe |

### Vulnerability Types

#### Horizontal Privilege Escalation
- **Detection**: B-type account gets same response as A-type
- **Example**: User A can access User B's profile
- **Risk**: Medium to High

#### Vertical Privilege Escalation
- **Detection**: C-type account gets admin-level response
- **Example**: Regular user can access admin functions
- **Risk**: High to Critical

### False Positive Indicators

1. **Dynamic Content**: Timestamps, session IDs, nonces
2. **Pagination**: Different page numbers or offsets
3. **Personalization**: User-specific content
4. **Rate Limiting**: Different response times

## Best Practices

### 1. Account Preparation

- **Use Real Accounts**: Create actual accounts with appropriate privileges
- **Fresh Sessions**: Use recently authenticated sessions
- **Valid Tokens**: Ensure tokens haven't expired
- **Different Resources**: Accounts should have access to different data

### 2. Scope Configuration

```yaml
# Good scope configuration
target_scope:
  - "https://api.example.com/v1/"
  - "https://api.example.com/v2/"
  - "https://admin.example.com/api/"

# Avoid overly broad scopes
# - "https://example.com/" (too broad)
```

### 3. Blacklist Management

```yaml
# Common patterns to blacklist
blacklist:
  - "google-analytics.com"
  - "googletagmanager.com"
  - "facebook.com/tr"
  - "/static/"
  - "/assets/"
  - ".css"
  - ".js"
  - ".png"
  - ".jpg"
```

### 4. Testing Workflow

1. **Start with Safe Operations**: Begin with GET requests
2. **Test Incrementally**: Add more destructive operations gradually
3. **Monitor Results**: Review results regularly
4. **Document Findings**: Add notes to significant results
5. **Verify Manually**: Confirm automated findings

## Troubleshooting

### Common Issues

#### 1. No Requests Captured

**Symptoms**: Empty results table, no traffic in logs

**Solutions**:
- Verify browser proxy settings
- Check target scope configuration
- Ensure URLs match scope patterns
- Review blacklist for over-blocking

#### 2. High False Positive Rate

**Symptoms**: Many false vulnerabilities detected

**Solutions**:
- Increase similarity threshold (0.9+)
- Review response cleaning patterns
- Add dynamic content patterns to cleaning
- Use manual verification

#### 3. Authentication Issues

**Symptoms**: 401/403 responses for all accounts

**Solutions**:
- Verify token validity
- Check token format and headers
- Ensure accounts have proper permissions
- Test accounts manually first

#### 4. Performance Issues

**Symptoms**: Slow response times, high CPU usage

**Solutions**:
- Reduce max_concurrency setting
- Increase request_timeout
- Limit target scope
- Add more blacklist patterns

### Debug Mode

Enable verbose logging for detailed troubleshooting:

```bash
./auth-tester --verbose
```

Check logs for:
- Request interception details
- Account credential application
- Response comparison results
- Error messages and stack traces

### Manual Verification

Always verify automated findings manually:

1. **Export Results**: Use API to get detailed request/response data
2. **Replay Manually**: Use tools like curl or Postman
3. **Compare Responses**: Look for sensitive data exposure
4. **Test Edge Cases**: Try boundary conditions
5. **Document Findings**: Record confirmed vulnerabilities

## Advanced Configuration

### Custom Similarity Algorithms

The tool supports multiple similarity algorithms:

- **Edit Distance**: Character-level comparison
- **Jaccard Similarity**: Word-based comparison
- **Cosine Similarity**: Vector-based comparison

### Response Cleaning

Customize response cleaning patterns:

```go
// Add custom patterns in pkg/similarity/similarity.go
patterns := []string{
    `"timestamp":\s*\d+`,
    `"session_id":\s*"[^"]*"`,
    `"csrf_token":\s*"[^"]*"`,
}
```

### Priority Customization

Fine-tune account priorities:

```json
{
  "name": "Manager",
  "type": "custom",
  "priority": 75,  // Between regular user (50) and admin (-1)
  "headers": [...]
}
```

This guide should help you effectively use the Authentication Tester to identify privilege escalation vulnerabilities in your applications.
