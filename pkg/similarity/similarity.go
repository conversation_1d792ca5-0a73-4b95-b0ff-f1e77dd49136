package similarity

import (
	"math"
	"strings"
	"unicode/utf8"
)

// CalculateStringSimilarity calculates similarity between two strings using edit distance
func CalculateStringSimilarity(s1, s2 string) float64 {
	if s1 == s2 {
		return 1.0
	}

	if len(s1) == 0 || len(s2) == 0 {
		return 0.0
	}

	// Use Levenshtein distance
	distance := LevenshteinDistance(s1, s2)
	maxLen := math.Max(float64(utf8.RuneCountInString(s1)), float64(utf8.RuneCountInString(s2)))
	
	if maxLen == 0 {
		return 1.0
	}

	return 1.0 - (float64(distance) / maxLen)
}

// LevenshteinDistance calculates the Levenshtein distance between two strings
func LevenshteinDistance(s1, s2 string) int {
	r1 := []rune(s1)
	r2 := []rune(s2)
	
	len1 := len(r1)
	len2 := len(r2)

	// Create a matrix to store distances
	matrix := make([][]int, len1+1)
	for i := range matrix {
		matrix[i] = make([]int, len2+1)
	}

	// Initialize first row and column
	for i := 0; i <= len1; i++ {
		matrix[i][0] = i
	}
	for j := 0; j <= len2; j++ {
		matrix[0][j] = j
	}

	// Fill the matrix
	for i := 1; i <= len1; i++ {
		for j := 1; j <= len2; j++ {
			cost := 0
			if r1[i-1] != r2[j-1] {
				cost = 1
			}

			matrix[i][j] = min(
				matrix[i-1][j]+1,      // deletion
				matrix[i][j-1]+1,      // insertion
				matrix[i-1][j-1]+cost, // substitution
			)
		}
	}

	return matrix[len1][len2]
}

// CalculateJaccardSimilarity calculates Jaccard similarity between two strings
func CalculateJaccardSimilarity(s1, s2 string) float64 {
	set1 := stringToWordSet(s1)
	set2 := stringToWordSet(s2)

	intersection := 0
	for word := range set1 {
		if set2[word] {
			intersection++
		}
	}

	union := len(set1) + len(set2) - intersection
	if union == 0 {
		return 1.0
	}

	return float64(intersection) / float64(union)
}

// CalculateCosineSimilarity calculates cosine similarity between two strings
func CalculateCosineSimilarity(s1, s2 string) float64 {
	vec1 := stringToWordVector(s1)
	vec2 := stringToWordVector(s2)

	dotProduct := 0.0
	norm1 := 0.0
	norm2 := 0.0

	// Get all unique words
	allWords := make(map[string]bool)
	for word := range vec1 {
		allWords[word] = true
	}
	for word := range vec2 {
		allWords[word] = true
	}

	// Calculate dot product and norms
	for word := range allWords {
		freq1 := float64(vec1[word])
		freq2 := float64(vec2[word])

		dotProduct += freq1 * freq2
		norm1 += freq1 * freq1
		norm2 += freq2 * freq2
	}

	if norm1 == 0 || norm2 == 0 {
		return 0.0
	}

	return dotProduct / (math.Sqrt(norm1) * math.Sqrt(norm2))
}

// CalculateResponseSimilarity calculates similarity specifically for HTTP responses
func CalculateResponseSimilarity(resp1, resp2 string) float64 {
	// Clean responses (remove timestamps, session IDs, etc.)
	cleaned1 := cleanResponse(resp1)
	cleaned2 := cleanResponse(resp2)

	// Use multiple similarity metrics and take the average
	editSim := CalculateStringSimilarity(cleaned1, cleaned2)
	jaccardSim := CalculateJaccardSimilarity(cleaned1, cleaned2)
	cosineSim := CalculateCosineSimilarity(cleaned1, cleaned2)

	// Weighted average (edit distance gets more weight for exact matching)
	return (editSim*0.5 + jaccardSim*0.3 + cosineSim*0.2)
}

// cleanResponse removes dynamic content from responses
func cleanResponse(response string) string {
	// Remove common dynamic patterns
	cleaned := response

	// Remove timestamps (various formats)
	patterns := []string{
		`\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}`,
		`\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}`,
		`\d{13}`, // Unix timestamp in milliseconds
		`\d{10}`, // Unix timestamp in seconds
	}

	for _, pattern := range patterns {
		// Simple replacement - in production, use regex
		cleaned = strings.ReplaceAll(cleaned, pattern, "TIMESTAMP")
	}

	// Remove session IDs and tokens (common patterns)
	sessionPatterns := []string{
		`"sessionId":"[^"]*"`,
		`"token":"[^"]*"`,
		`"csrf":"[^"]*"`,
		`"nonce":"[^"]*"`,
	}

	for _, pattern := range sessionPatterns {
		cleaned = strings.ReplaceAll(cleaned, pattern, `"sessionId":"SESSION_ID"`)
	}

	return cleaned
}

// stringToWordSet converts a string to a set of words
func stringToWordSet(s string) map[string]bool {
	words := strings.Fields(strings.ToLower(s))
	set := make(map[string]bool)
	for _, word := range words {
		set[word] = true
	}
	return set
}

// stringToWordVector converts a string to a word frequency vector
func stringToWordVector(s string) map[string]int {
	words := strings.Fields(strings.ToLower(s))
	vector := make(map[string]int)
	for _, word := range words {
		vector[word]++
	}
	return vector
}

// min returns the minimum of three integers
func min(a, b, c int) int {
	if a < b {
		if a < c {
			return a
		}
		return c
	}
	if b < c {
		return b
	}
	return c
}
