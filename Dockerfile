# Build stage
FROM golang:1.21-alpine AS builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache git

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o auth-tester cmd/auth-tester/main.go

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates sqlite

WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/auth-tester .

# Copy configuration and web assets
COPY --from=builder /app/config.yaml .
COPY --from=builder /app/web ./web

# Create data directory
RUN mkdir -p /data

# Expose ports
EXPOSE 8080 8081

# Set environment variables
ENV GIN_MODE=release

# Run the application
CMD ["./auth-tester", "--config", "config.yaml"]
