#!/bin/bash

# Authentication Tester Setup Script
set -e

echo "🔐 Authentication Tester Setup"
echo "==============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Go is installed
check_go() {
    if command -v go &> /dev/null; then
        GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
        print_success "Go $GO_VERSION is installed"
        return 0
    else
        print_error "Go is not installed"
        return 1
    fi
}

# Install Go if not present
install_go() {
    print_status "Installing Go..."
    
    # Detect OS
    OS=$(uname -s | tr '[:upper:]' '[:lower:]')
    ARCH=$(uname -m)
    
    case $ARCH in
        x86_64) ARCH="amd64" ;;
        arm64|aarch64) ARCH="arm64" ;;
        armv6l) ARCH="armv6l" ;;
        *) print_error "Unsupported architecture: $ARCH"; exit 1 ;;
    esac
    
    GO_VERSION="1.21.5"
    GO_TARBALL="go${GO_VERSION}.${OS}-${ARCH}.tar.gz"
    GO_URL="https://golang.org/dl/${GO_TARBALL}"
    
    print_status "Downloading Go ${GO_VERSION} for ${OS}-${ARCH}..."
    
    # Download Go
    if command -v curl &> /dev/null; then
        curl -L -o "/tmp/${GO_TARBALL}" "${GO_URL}"
    elif command -v wget &> /dev/null; then
        wget -O "/tmp/${GO_TARBALL}" "${GO_URL}"
    else
        print_error "Neither curl nor wget is available. Please install Go manually."
        exit 1
    fi
    
    # Install Go
    print_status "Installing Go to /usr/local..."
    sudo rm -rf /usr/local/go
    sudo tar -C /usr/local -xzf "/tmp/${GO_TARBALL}"
    
    # Add Go to PATH
    if ! grep -q "/usr/local/go/bin" ~/.bashrc; then
        echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
        print_status "Added Go to PATH in ~/.bashrc"
    fi
    
    if ! grep -q "/usr/local/go/bin" ~/.profile; then
        echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.profile
        print_status "Added Go to PATH in ~/.profile"
    fi
    
    # Set PATH for current session
    export PATH=$PATH:/usr/local/go/bin
    
    # Clean up
    rm "/tmp/${GO_TARBALL}"
    
    print_success "Go installed successfully"
}

# Build the application
build_app() {
    print_status "Building Authentication Tester..."
    
    # Install dependencies
    go mod tidy
    
    # Build the application
    go build -o auth-tester cmd/auth-tester/main.go
    
    print_success "Application built successfully"
}

# Create necessary directories
setup_directories() {
    print_status "Setting up directories..."
    
    mkdir -p web/static/css
    mkdir -p web/static/js
    mkdir -p web/static/img
    mkdir -p configs
    mkdir -p logs
    
    print_success "Directories created"
}

# Install system dependencies
install_system_deps() {
    print_status "Installing system dependencies..."
    
    if command -v apt-get &> /dev/null; then
        # Debian/Ubuntu
        sudo apt-get update
        sudo apt-get install -y curl wget git build-essential
    elif command -v yum &> /dev/null; then
        # RHEL/CentOS
        sudo yum install -y curl wget git gcc
    elif command -v brew &> /dev/null; then
        # macOS
        brew install git
    else
        print_warning "Unknown package manager. Please install curl, wget, and git manually."
    fi
}

# Create systemd service (Linux only)
create_service() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_status "Creating systemd service..."
        
        CURRENT_DIR=$(pwd)
        SERVICE_FILE="/etc/systemd/system/auth-tester.service"
        
        sudo tee "$SERVICE_FILE" > /dev/null <<EOF
[Unit]
Description=Authentication Tester
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$CURRENT_DIR
ExecStart=$CURRENT_DIR/auth-tester
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF
        
        sudo systemctl daemon-reload
        print_success "Systemd service created. Use 'sudo systemctl start auth-tester' to start."
    fi
}

# Main setup function
main() {
    print_status "Starting setup process..."
    
    # Install system dependencies
    install_system_deps
    
    # Check and install Go if needed
    if ! check_go; then
        print_status "Go not found. Installing..."
        install_go
        
        # Verify installation
        if ! check_go; then
            print_error "Go installation failed. Please install Go manually."
            exit 1
        fi
    fi
    
    # Setup directories
    setup_directories
    
    # Build application
    build_app
    
    # Create systemd service (Linux only)
    create_service
    
    print_success "Setup completed successfully!"
    echo ""
    echo "🚀 Quick Start:"
    echo "  1. Run: ./auth-tester"
    echo "  2. Open: http://localhost:8080"
    echo "  3. Configure proxy: localhost:8081"
    echo ""
    echo "📚 Documentation:"
    echo "  - README.md for detailed usage"
    echo "  - examples/example_usage.go for programmatic usage"
    echo ""
    echo "🐳 Docker Alternative:"
    echo "  docker build -t auth-tester ."
    echo "  docker run -p 8080:8080 -p 8081:8081 auth-tester"
}

# Run main function
main "$@"
