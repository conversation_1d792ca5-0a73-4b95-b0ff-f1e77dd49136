# Authentication Tester

A comprehensive authentication testing framework inspired by Burp Suite's authentication testing capabilities. This tool automates privilege escalation testing, request replay, and response comparison to identify horizontal and vertical privilege escalation vulnerabilities.

## Features

- **Multi-Account Testing**: Support for different account types (baseline, same privilege, low privilege, custom)
- **Automated Request Replay**: Automatically replays requests with different authentication credentials
- **Response Similarity Analysis**: Uses multiple algorithms (edit distance, Jaccard, cosine similarity) to compare responses
- **Priority-Based Execution**: Executes requests in optimal order to handle destructive operations
- **Web-Based Management**: Intuitive web interface for configuration and result analysis
- **HTTP Proxy Integration**: Intercepts and analyzes HTTP traffic
- **Blacklist Management**: Exclude unwanted requests from testing
- **Real-time Results**: Live updates of test results and vulnerability detection

## Architecture

The framework is built with the following components:

- **Proxy Server**: Intercepts HTTP requests and forwards them for analysis
- **Replay Engine**: Handles request replay with different authentication credentials
- **Similarity Engine**: Compares responses to detect privilege escalation
- **Web Interface**: Management dashboard for configuration and results
- **Storage Layer**: SQLite database for persistent storage

## Installation

1. **Prerequisites**:
   - Go 1.21 or later
   - Git

2. **Clone and Build**:
   ```bash
   git clone <repository-url>
   cd auth-tester
   go mod tidy
   go build -o auth-tester cmd/auth-tester/main.go
   ```

3. **Run**:
   ```bash
   ./auth-tester
   ```

## Usage

### 1. Start the Application

```bash
./auth-tester --port 8080 --proxy-port 8081 --verbose
```

- Web interface will be available at `http://localhost:8080`
- Proxy server will listen on port `8081`

### 2. Configure Browser Proxy

Configure your browser to use `localhost:8081` as HTTP proxy:

**Chrome/Chromium**:
```bash
google-chrome --proxy-server="localhost:8081"
```

**Firefox**: Go to Settings → Network Settings → Manual proxy configuration

### 3. Create Test Profile

1. Open the web interface at `http://localhost:8080`
2. Click "New Profile"
3. Configure:
   - **Name**: Descriptive name for your test
   - **Description**: Optional description
   - **Target Scope**: URLs to test (one per line)
   - **Similarity Threshold**: Threshold for vulnerability detection (0.0-1.0)

### 4. Add Accounts

For each test profile, add accounts representing different privilege levels:

#### Account Types

- **Baseline (A-type)**: Reference account with full privileges
- **Same Privilege (B-type)**: Account with same privileges (for horizontal testing)
- **Low Privilege (C-type)**: Account with lower privileges (for vertical testing)
- **Custom (D-type)**: Custom role account

#### Account Configuration

For each account, configure:
- **Headers**: Authentication headers (e.g., `Authorization: Bearer token`)
- **Cookies**: Session cookies (e.g., `sessionid=abc123`)
- **Parameters**: URL parameters (if needed)

### 5. Start Testing

1. Activate a profile by clicking "Activate"
2. Browse the target application normally
3. The tool will automatically:
   - Intercept requests matching the target scope
   - Replay requests with different account credentials
   - Compare responses and detect potential vulnerabilities
   - Display results in real-time

### 6. Analyze Results

The results table shows:
- **Method**: HTTP method (GET, POST, etc.)
- **URL**: Request URL
- **Account**: Account used for testing
- **Similarity**: Response similarity score
- **Status**: Vulnerable/Safe status
- **Type**: Vulnerability type (horizontal/vertical)

## Configuration

### Configuration File

Create a `config.yaml` file:

```yaml
database:
  path: "auth-tester.db"

proxy:
  port: 8081
  blocked_patterns:
    - "google-analytics.com"
    - "googletagmanager.com"

web:
  port: 8080

logging:
  level: "info"
```

### Command Line Options

```bash
./auth-tester --help
```

Options:
- `--config, -c`: Configuration file path (default: config.yaml)
- `--port, -p`: Web interface port (default: 8080)
- `--proxy-port, -P`: Proxy server port (default: 8081)
- `--verbose, -v`: Enable verbose logging

## Testing Methodology

### Account Priority System

The tool executes requests in priority order to handle destructive operations:

1. **C-type (Low privilege)**: Priority 101 - Executed first
2. **D-type (Custom)**: Priority 1-100 - Configurable
3. **B-type (Same privilege)**: Priority 50 - Executed second
4. **A-type (Baseline)**: Priority -1 - Executed last (reference)

### Vulnerability Detection

The tool detects vulnerabilities by:

1. **Response Comparison**: Comparing test responses with baseline responses
2. **Similarity Scoring**: Using multiple algorithms for accurate comparison
3. **Status Code Analysis**: Considering HTTP status codes
4. **Threshold-based Detection**: Configurable similarity thresholds

### Best Practices

1. **Account Setup**:
   - Use real accounts with appropriate privileges
   - Ensure accounts have access to different resources
   - Test with both valid and invalid credentials

2. **Scope Configuration**:
   - Include API endpoints and web pages
   - Exclude static resources and analytics
   - Use specific URL patterns for better targeting

3. **Result Analysis**:
   - Review high similarity scores manually
   - Check for false positives
   - Document findings with notes

## API Reference

### Profiles

- `GET /api/profiles` - List all profiles
- `POST /api/profiles` - Create new profile
- `PUT /api/profiles/:id/activate` - Activate profile
- `DELETE /api/profiles/:id` - Delete profile

### Accounts

- `POST /api/profiles/:id/accounts` - Add account to profile
- `PUT /api/accounts/:id` - Update account
- `DELETE /api/accounts/:id` - Delete account

### Results

- `GET /api/profiles/:id/results` - Get test results
- `PUT /api/results/:id/mark-vulnerable` - Mark result as vulnerable
- `PUT /api/results/:id/add-note` - Add note to result

## Troubleshooting

### Common Issues

1. **Proxy not working**:
   - Check browser proxy settings
   - Verify proxy port is not in use
   - Check firewall settings

2. **No requests captured**:
   - Verify target scope configuration
   - Check if URLs match the scope patterns
   - Review blacklist patterns

3. **False positives**:
   - Adjust similarity threshold
   - Review response cleaning patterns
   - Add notes to mark false positives

### Debugging

Enable verbose logging:
```bash
./auth-tester --verbose
```

Check logs for detailed information about request processing and errors.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Inspired by the Burp Suite authentication testing tutorial
- Built with Go, Gin, GORM, and Bootstrap
- Uses various similarity algorithms for response comparison
