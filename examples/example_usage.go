package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

// Example demonstrating how to programmatically create test profiles and accounts
func main() {
	baseURL := "http://localhost:8080/api"
	
	// Create a test profile
	profile := map[string]interface{}{
		"name":        "Example API Test",
		"description": "Testing authentication for example API",
		"target_scope": []string{
			"https://api.example.com/",
			"https://app.example.com/api/",
		},
		"settings": map[string]interface{}{
			"similarity_threshold": 0.8,
			"enable_replay":       true,
			"max_concurrency":     5,
			"request_timeout":     30,
		},
	}
	
	profileID, err := createProfile(baseURL, profile)
	if err != nil {
		fmt.Printf("Failed to create profile: %v\n", err)
		return
	}
	
	fmt.Printf("Created profile with ID: %d\n", profileID)
	
	// Create accounts for different privilege levels
	accounts := []map[string]interface{}{
		{
			"name":     "Admin User",
			"type":     "baseline",
			"priority": -1,
			"headers": []map[string]string{
				{"name": "Authorization", "value": "Bearer admin_token_here"},
				{"name": "X-User-Role", "value": "admin"},
			},
			"cookies": []map[string]string{
				{"name": "sessionid", "value": "admin_session_id"},
			},
		},
		{
			"name":     "Regular User",
			"type":     "same",
			"priority": 50,
			"headers": []map[string]string{
				{"name": "Authorization", "value": "Bearer user_token_here"},
				{"name": "X-User-Role", "value": "user"},
			},
			"cookies": []map[string]string{
				{"name": "sessionid", "value": "user_session_id"},
			},
		},
		{
			"name":     "Guest User",
			"type":     "low",
			"priority": 101,
			"headers": []map[string]string{
				{"name": "Authorization", "value": "Bearer guest_token_here"},
				{"name": "X-User-Role", "value": "guest"},
			},
			"cookies": []map[string]string{
				{"name": "sessionid", "value": "guest_session_id"},
			},
		},
	}
	
	for _, account := range accounts {
		accountID, err := createAccount(baseURL, profileID, account)
		if err != nil {
			fmt.Printf("Failed to create account %s: %v\n", account["name"], err)
			continue
		}
		fmt.Printf("Created account '%s' with ID: %d\n", account["name"], accountID)
	}
	
	// Activate the profile
	err = activateProfile(baseURL, profileID)
	if err != nil {
		fmt.Printf("Failed to activate profile: %v\n", err)
		return
	}
	
	fmt.Printf("Profile activated successfully!\n")
	fmt.Printf("You can now:\n")
	fmt.Printf("1. Configure your browser to use proxy localhost:8081\n")
	fmt.Printf("2. Browse the target application\n")
	fmt.Printf("3. View results at http://localhost:8080\n")
}

func createProfile(baseURL string, profile map[string]interface{}) (int, error) {
	jsonData, err := json.Marshal(profile)
	if err != nil {
		return 0, err
	}
	
	resp, err := http.Post(baseURL+"/profiles", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusCreated {
		return 0, fmt.Errorf("failed to create profile: status %d", resp.StatusCode)
	}
	
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return 0, err
	}
	
	return int(result["id"].(float64)), nil
}

func createAccount(baseURL string, profileID int, account map[string]interface{}) (int, error) {
	jsonData, err := json.Marshal(account)
	if err != nil {
		return 0, err
	}
	
	url := fmt.Sprintf("%s/profiles/%d/accounts", baseURL, profileID)
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusCreated {
		return 0, fmt.Errorf("failed to create account: status %d", resp.StatusCode)
	}
	
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return 0, err
	}
	
	return int(result["id"].(float64)), nil
}

func activateProfile(baseURL string, profileID int) error {
	url := fmt.Sprintf("%s/profiles/%d/activate", baseURL, profileID)
	req, err := http.NewRequest("PUT", url, nil)
	if err != nil {
		return err
	}
	
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to activate profile: status %d", resp.StatusCode)
	}
	
	return nil
}
