.PHONY: build run test clean install deps example

# Build the application
build:
	go build -o auth-tester cmd/auth-tester/main.go

# Run the application
run: build
	./auth-tester

# Run with verbose logging
run-verbose: build
	./auth-tester --verbose

# Run tests
test:
	go test -v ./...

# Clean build artifacts
clean:
	rm -f auth-tester
	rm -f auth-tester.db

# Install dependencies
deps:
	go mod tidy
	go mod download

# Run the example
example: build
	go run examples/example_usage.go

# Install the application
install: build
	sudo cp auth-tester /usr/local/bin/

# Development setup
dev-setup: deps
	@echo "Setting up development environment..."
	@mkdir -p web/static/css web/static/js web/static/img
	@mkdir -p web/templates
	@mkdir -p configs
	@echo "Development environment ready!"

# Docker build
docker-build:
	docker build -t auth-tester .

# Docker run
docker-run: docker-build
	docker run -p 8080:8080 -p 8081:8081 auth-tester

# Format code
fmt:
	go fmt ./...

# Lint code
lint:
	golangci-lint run

# Security scan
security:
	gosec ./...

# Generate documentation
docs:
	godoc -http=:6060

# Help
help:
	@echo "Available targets:"
	@echo "  build        - Build the application"
	@echo "  run          - Build and run the application"
	@echo "  run-verbose  - Run with verbose logging"
	@echo "  test         - Run tests"
	@echo "  clean        - Clean build artifacts"
	@echo "  deps         - Install dependencies"
	@echo "  example      - Run the example usage"
	@echo "  install      - Install to /usr/local/bin"
	@echo "  dev-setup    - Set up development environment"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run in Docker"
	@echo "  fmt          - Format code"
	@echo "  lint         - Lint code"
	@echo "  security     - Run security scan"
	@echo "  docs         - Generate documentation"
	@echo "  help         - Show this help"
