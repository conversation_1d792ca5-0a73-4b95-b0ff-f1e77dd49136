package config

import (
	"fmt"
	"os"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Database DatabaseConfig `mapstructure:"database"`
	Proxy    ProxyConfig    `mapstructure:"proxy"`
	Web      WebConfig      `mapstructure:"web"`
	Logging  LoggingConfig  `mapstructure:"logging"`
}

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Path string `mapstructure:"path"`
}

// ProxyConfig holds proxy server configuration
type ProxyConfig struct {
	Port            int      `mapstructure:"port"`
	CertFile        string   `mapstructure:"cert_file"`
	KeyFile         string   `mapstructure:"key_file"`
	AllowedHosts    []string `mapstructure:"allowed_hosts"`
	BlockedPatterns []string `mapstructure:"blocked_patterns"`
}

// WebConfig holds web server configuration
type WebConfig struct {
	Port      int    `mapstructure:"port"`
	StaticDir string `mapstructure:"static_dir"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
}

// Load loads configuration from file
func Load(configFile string) (*Config, error) {
	viper.SetConfigFile(configFile)
	viper.SetConfigType("yaml")

	// Set defaults
	viper.SetDefault("database.path", "auth-tester.db")
	viper.SetDefault("proxy.port", 8081)
	viper.SetDefault("web.port", 8080)
	viper.SetDefault("web.static_dir", "web/static")
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "text")

	// Read config file if it exists
	if _, err := os.Stat(configFile); err == nil {
		if err := viper.ReadInConfig(); err != nil {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// TestProfile represents a testing configuration profile
type TestProfile struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"uniqueIndex"`
	Description string    `json:"description"`
	Active      bool      `json:"active"`
	TargetScope []string  `json:"target_scope" gorm:"serializer:json"`
	Blacklist   []string  `json:"blacklist" gorm:"serializer:json"`
	Accounts    []Account `json:"accounts" gorm:"foreignKey:ProfileID"`
	Settings    Settings  `json:"settings" gorm:"embedded"`
}

// Account represents an authentication account
type Account struct {
	ID        uint        `json:"id" gorm:"primaryKey"`
	ProfileID uint        `json:"profile_id"`
	Name      string      `json:"name"`
	Type      AccountType `json:"type"`
	Priority  int         `json:"priority"`
	Headers   []Header    `json:"headers" gorm:"foreignKey:AccountID"`
	Cookies   []Cookie    `json:"cookies" gorm:"foreignKey:AccountID"`
	Params    []Param     `json:"params" gorm:"foreignKey:AccountID"`
}

// AccountType represents the type of account
type AccountType string

const (
	AccountTypeBaseline AccountType = "baseline" // A-type: baseline account
	AccountTypeSame     AccountType = "same"     // B-type: same privilege
	AccountTypeLow      AccountType = "low"      // C-type: low/no privilege
	AccountTypeCustom   AccountType = "custom"   // D-type: custom role
)

// Header represents an HTTP header credential
type Header struct {
	ID        uint   `json:"id" gorm:"primaryKey"`
	AccountID uint   `json:"account_id"`
	Name      string `json:"name"`
	Value     string `json:"value"`
}

// Cookie represents an HTTP cookie credential
type Cookie struct {
	ID        uint   `json:"id" gorm:"primaryKey"`
	AccountID uint   `json:"account_id"`
	Name      string `json:"name"`
	Value     string `json:"value"`
}

// Param represents a request parameter credential
type Param struct {
	ID        uint   `json:"id" gorm:"primaryKey"`
	AccountID uint   `json:"account_id"`
	Name      string `json:"name"`
	Value     string `json:"value"`
}

// Settings represents profile settings
type Settings struct {
	EnableReplay       bool    `json:"enable_replay"`
	SimilarityThreshold float64 `json:"similarity_threshold"`
	MaxConcurrency     int     `json:"max_concurrency"`
	RequestTimeout     int     `json:"request_timeout"`
}
