package web

import (
	"fmt"
	"net/http"
	"strconv"

	"auth-tester/internal/config"
	"auth-tester/internal/proxy"
	"auth-tester/internal/storage"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// Server represents the web server
type Server struct {
	port        int
	db          *storage.DB
	proxyServer *proxy.Server
	router      *gin.Engine
}

// NewServer creates a new web server
func NewServer(port int, db *storage.DB, proxyServer *proxy.Server) *Server {
	router := gin.Default()
	
	server := &Server{
		port:        port,
		db:          db,
		proxyServer: proxyServer,
		router:      router,
	}

	server.setupRoutes()
	return server
}

// Start starts the web server
func (s *Server) Start() error {
	logrus.Infof("Web server starting on port %d", s.port)
	return s.router.Run(fmt.Sprintf(":%d", s.port))
}

// setupRoutes sets up the HTTP routes
func (s *Server) setupRoutes() {
	// Serve static files
	s.router.Static("/static", "./web/static")
	s.router.LoadHTMLGlob("web/templates/*")

	// Main dashboard
	s.router.GET("/", s.handleDashboard)

	// API routes
	api := s.router.Group("/api")
	{
		// Profile management
		api.GET("/profiles", s.handleGetProfiles)
		api.POST("/profiles", s.handleCreateProfile)
		api.PUT("/profiles/:id/activate", s.handleActivateProfile)
		api.DELETE("/profiles/:id", s.handleDeleteProfile)

		// Account management
		api.POST("/profiles/:id/accounts", s.handleCreateAccount)
		api.PUT("/accounts/:id", s.handleUpdateAccount)
		api.DELETE("/accounts/:id", s.handleDeleteAccount)

		// Test results
		api.GET("/profiles/:id/requests", s.handleGetRequests)
		api.GET("/profiles/:id/results", s.handleGetResults)
		api.PUT("/results/:id/mark-vulnerable", s.handleMarkVulnerable)
		api.PUT("/results/:id/add-note", s.handleAddNote)

		// Blacklist management
		api.POST("/profiles/:id/blacklist", s.handleAddToBlacklist)
		api.DELETE("/profiles/:id/blacklist", s.handleRemoveFromBlacklist)
	}
}

// handleDashboard serves the main dashboard
func (s *Server) handleDashboard(c *gin.Context) {
	c.HTML(http.StatusOK, "dashboard.html", gin.H{
		"title": "Authentication Tester Dashboard",
	})
}

// handleGetProfiles returns all test profiles
func (s *Server) handleGetProfiles(c *gin.Context) {
	profiles, err := s.db.GetProfiles()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, profiles)
}

// handleCreateProfile creates a new test profile
func (s *Server) handleCreateProfile(c *gin.Context) {
	var profile config.TestProfile
	if err := c.ShouldBindJSON(&profile); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set default settings
	if profile.Settings.SimilarityThreshold == 0 {
		profile.Settings.SimilarityThreshold = 0.8
	}
	if profile.Settings.MaxConcurrency == 0 {
		profile.Settings.MaxConcurrency = 5
	}
	if profile.Settings.RequestTimeout == 0 {
		profile.Settings.RequestTimeout = 30
	}

	if err := s.db.CreateProfile(&profile); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, profile)
}

// handleActivateProfile activates a test profile
func (s *Server) handleActivateProfile(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid profile ID"})
		return
	}

	if err := s.db.SetActiveProfile(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Profile activated"})
}

// handleDeleteProfile deletes a test profile
func (s *Server) handleDeleteProfile(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid profile ID"})
		return
	}

	if err := s.db.Delete(&config.TestProfile{}, uint(id)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Profile deleted"})
}

// handleCreateAccount creates a new account for a profile
func (s *Server) handleCreateAccount(c *gin.Context) {
	idStr := c.Param("id")
	profileID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid profile ID"})
		return
	}

	var account config.Account
	if err := c.ShouldBindJSON(&account); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	account.ProfileID = uint(profileID)

	// Set default priority based on account type
	if account.Priority == 0 {
		switch account.Type {
		case config.AccountTypeBaseline:
			account.Priority = -1
		case config.AccountTypeSame:
			account.Priority = 50
		case config.AccountTypeLow:
			account.Priority = 101
		case config.AccountTypeCustom:
			account.Priority = 75
		}
	}

	if err := s.db.Create(&account).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, account)
}

// handleUpdateAccount updates an existing account
func (s *Server) handleUpdateAccount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
		return
	}

	var account config.Account
	if err := c.ShouldBindJSON(&account); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	account.ID = uint(id)
	if err := s.db.Save(&account).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, account)
}

// handleDeleteAccount deletes an account
func (s *Server) handleDeleteAccount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
		return
	}

	if err := s.db.Delete(&config.Account{}, uint(id)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Account deleted"})
}

// handleGetRequests returns requests for a profile
func (s *Server) handleGetRequests(c *gin.Context) {
	idStr := c.Param("id")
	profileID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid profile ID"})
		return
	}

	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)

	requests, err := s.db.GetRequestsByProfile(uint(profileID), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, requests)
}

// handleGetResults returns test results for a profile
func (s *Server) handleGetResults(c *gin.Context) {
	idStr := c.Param("id")
	profileID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid profile ID"})
		return
	}

	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)

	results, err := s.db.GetTestResults(uint(profileID), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, results)
}

// handleMarkVulnerable marks a test result as vulnerable
func (s *Server) handleMarkVulnerable(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid result ID"})
		return
	}

	var req struct {
		IsVulnerable bool `json:"is_vulnerable"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := s.db.Model(&storage.TestResult{}).Where("id = ?", uint(id)).Update("is_vulnerable", req.IsVulnerable).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Result updated"})
}

// handleAddNote adds a note to a test result
func (s *Server) handleAddNote(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid result ID"})
		return
	}

	var req struct {
		Note string `json:"note"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := s.db.Model(&storage.TestResult{}).Where("id = ?", uint(id)).Update("notes", req.Note).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Note added"})
}
