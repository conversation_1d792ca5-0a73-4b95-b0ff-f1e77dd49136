package storage

import (
	"time"

	"auth-tester/internal/config"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// DB represents the database connection
type DB struct {
	*gorm.DB
}

// Initialize initializes the database connection and runs migrations
func Initialize(dbPath string) (*DB, error) {
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(
		&config.TestProfile{},
		&config.Account{},
		&config.Header{},
		&config.Cookie{},
		&config.Param{},
		&RequestRecord{},
		&ResponseRecord{},
		&TestResult{},
	)
	if err != nil {
		return nil, err
	}

	return &DB{db}, nil
}

// RequestRecord represents a captured HTTP request
type RequestRecord struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	ProfileID uint      `json:"profile_id"`
	Method    string    `json:"method"`
	URL       string    `json:"url"`
	Headers   string    `json:"headers" gorm:"type:text"`
	Body      string    `json:"body" gorm:"type:text"`
	Timestamp time.Time `json:"timestamp"`
	Hash      string    `json:"hash" gorm:"uniqueIndex"`
	
	// Relationships
	Responses []ResponseRecord `json:"responses" gorm:"foreignKey:RequestID"`
	Results   []TestResult     `json:"results" gorm:"foreignKey:RequestID"`
}

// ResponseRecord represents an HTTP response
type ResponseRecord struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	RequestID uint      `json:"request_id"`
	AccountID uint      `json:"account_id"`
	StatusCode int      `json:"status_code"`
	Headers   string    `json:"headers" gorm:"type:text"`
	Body      string    `json:"body" gorm:"type:text"`
	Size      int64     `json:"size"`
	Duration  int64     `json:"duration"` // in milliseconds
	Timestamp time.Time `json:"timestamp"`
	
	// Relationships
	Account config.Account `json:"account" gorm:"foreignKey:AccountID"`
}

// TestResult represents the result of authentication testing
type TestResult struct {
	ID               uint      `json:"id" gorm:"primaryKey"`
	RequestID        uint      `json:"request_id"`
	BaselineResponse uint      `json:"baseline_response_id"`
	TestResponse     uint      `json:"test_response_id"`
	Similarity       float64   `json:"similarity"`
	IsVulnerable     bool      `json:"is_vulnerable"`
	VulnType         string    `json:"vuln_type"` // horizontal, vertical
	Notes            string    `json:"notes" gorm:"type:text"`
	Timestamp        time.Time `json:"timestamp"`
	
	// Relationships
	Request          RequestRecord  `json:"request" gorm:"foreignKey:RequestID"`
	BaselineResp     ResponseRecord `json:"baseline_resp" gorm:"foreignKey:BaselineResponse"`
	TestResp         ResponseRecord `json:"test_resp" gorm:"foreignKey:TestResponse"`
}

// GetActiveProfile returns the currently active test profile
func (db *DB) GetActiveProfile() (*config.TestProfile, error) {
	var profile config.TestProfile
	err := db.Preload("Accounts").Preload("Accounts.Headers").
		Preload("Accounts.Cookies").Preload("Accounts.Params").
		Where("active = ?", true).First(&profile).Error
	return &profile, err
}

// SetActiveProfile sets a profile as active and deactivates others
func (db *DB) SetActiveProfile(profileID uint) error {
	// Deactivate all profiles
	if err := db.Model(&config.TestProfile{}).Update("active", false).Error; err != nil {
		return err
	}
	
	// Activate the specified profile
	return db.Model(&config.TestProfile{}).Where("id = ?", profileID).Update("active", true).Error
}

// CreateProfile creates a new test profile
func (db *DB) CreateProfile(profile *config.TestProfile) error {
	return db.Create(profile).Error
}

// GetProfiles returns all test profiles
func (db *DB) GetProfiles() ([]config.TestProfile, error) {
	var profiles []config.TestProfile
	err := db.Preload("Accounts").Find(&profiles).Error
	return profiles, err
}

// SaveRequest saves a request record
func (db *DB) SaveRequest(request *RequestRecord) error {
	return db.Create(request).Error
}

// SaveResponse saves a response record
func (db *DB) SaveResponse(response *ResponseRecord) error {
	return db.Create(response).Error
}

// SaveTestResult saves a test result
func (db *DB) SaveTestResult(result *TestResult) error {
	return db.Create(result).Error
}

// GetRequestsByProfile returns requests for a specific profile
func (db *DB) GetRequestsByProfile(profileID uint, limit, offset int) ([]RequestRecord, error) {
	var requests []RequestRecord
	err := db.Preload("Responses").Preload("Results").
		Where("profile_id = ?", profileID).
		Order("timestamp DESC").
		Limit(limit).Offset(offset).
		Find(&requests).Error
	return requests, err
}

// GetTestResults returns test results with pagination
func (db *DB) GetTestResults(profileID uint, limit, offset int) ([]TestResult, error) {
	var results []TestResult
	err := db.Preload("Request").Preload("BaselineResp").Preload("TestResp").
		Joins("JOIN request_records ON test_results.request_id = request_records.id").
		Where("request_records.profile_id = ?", profileID).
		Order("test_results.timestamp DESC").
		Limit(limit).Offset(offset).
		Find(&results).Error
	return results, err
}
