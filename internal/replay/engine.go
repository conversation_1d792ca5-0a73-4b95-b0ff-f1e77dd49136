package replay

import (
	"bytes"
	"crypto/md5"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
	"time"

	"auth-tester/internal/config"
	"auth-tester/internal/storage"
	"auth-tester/pkg/similarity"

	"github.com/sirupsen/logrus"
)

// Engine handles request replay and authentication testing
type Engine struct {
	db     *storage.DB
	client *http.Client
}

// NewEngine creates a new replay engine
func NewEngine(db *storage.DB) *Engine {
	return &Engine{
		db: db,
		client: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			},
		},
	}
}

// ProcessRequest processes a request for authentication testing
func (e *Engine) ProcessRequest(originalReq *http.Request, originalResp *http.Response) {
	// Get active profile
	profile, err := e.db.GetActiveProfile()
	if err != nil {
		logrus.Errorf("Failed to get active profile: %v", err)
		return
	}

	if !profile.Settings.EnableReplay {
		return
	}

	// Create request record
	reqRecord, err := e.createRequestRecord(originalReq, profile.ID)
	if err != nil {
		logrus.Errorf("Failed to create request record: %v", err)
		return
	}

	// Save original response as baseline
	baselineResp, err := e.saveResponse(originalResp, reqRecord.ID, 0) // 0 for baseline
	if err != nil {
		logrus.Errorf("Failed to save baseline response: %v", err)
		return
	}

	// Get accounts sorted by priority (highest first)
	accounts := e.getSortedAccounts(profile.Accounts)

	// Replay requests with different accounts
	for _, account := range accounts {
		if account.Type == config.AccountTypeBaseline {
			continue // Skip baseline account as we already have the response
		}

		e.replayWithAccount(originalReq, reqRecord, baselineResp, account)
	}
}

// createRequestRecord creates a request record from HTTP request
func (e *Engine) createRequestRecord(req *http.Request, profileID uint) (*storage.RequestRecord, error) {
	// Read body
	var bodyBytes []byte
	if req.Body != nil {
		bodyBytes, _ = io.ReadAll(req.Body)
		req.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	}

	// Serialize headers
	headersJSON, _ := json.Marshal(req.Header)

	// Create hash for deduplication
	hash := e.createRequestHash(req.Method, req.URL.String(), string(headersJSON), string(bodyBytes))

	reqRecord := &storage.RequestRecord{
		ProfileID: profileID,
		Method:    req.Method,
		URL:       req.URL.String(),
		Headers:   string(headersJSON),
		Body:      string(bodyBytes),
		Timestamp: time.Now(),
		Hash:      hash,
	}

	// Check if request already exists
	var existing storage.RequestRecord
	if err := e.db.Where("hash = ?", hash).First(&existing).Error; err == nil {
		return &existing, nil
	}

	// Save new request
	if err := e.db.SaveRequest(reqRecord); err != nil {
		return nil, err
	}

	return reqRecord, nil
}

// saveResponse saves an HTTP response
func (e *Engine) saveResponse(resp *http.Response, requestID uint, accountID uint) (*storage.ResponseRecord, error) {
	// Read body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	resp.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// Serialize headers
	headersJSON, _ := json.Marshal(resp.Header)

	respRecord := &storage.ResponseRecord{
		RequestID:  requestID,
		AccountID:  accountID,
		StatusCode: resp.StatusCode,
		Headers:    string(headersJSON),
		Body:       string(bodyBytes),
		Size:       int64(len(bodyBytes)),
		Timestamp:  time.Now(),
	}

	if err := e.db.SaveResponse(respRecord); err != nil {
		return nil, err
	}

	return respRecord, nil
}

// replayWithAccount replays a request with a specific account's credentials
func (e *Engine) replayWithAccount(originalReq *http.Request, reqRecord *storage.RequestRecord, baselineResp *storage.ResponseRecord, account config.Account) {
	// Clone the original request
	clonedReq, err := e.cloneRequest(originalReq)
	if err != nil {
		logrus.Errorf("Failed to clone request: %v", err)
		return
	}

	// Apply account credentials
	e.applyCredentials(clonedReq, account)

	// Execute request
	start := time.Now()
	resp, err := e.client.Do(clonedReq)
	duration := time.Since(start).Milliseconds()

	if err != nil {
		logrus.Errorf("Failed to replay request with account %s: %v", account.Name, err)
		return
	}
	defer resp.Body.Close()

	// Save response
	testResp, err := e.saveResponse(resp, reqRecord.ID, account.ID)
	if err != nil {
		logrus.Errorf("Failed to save test response: %v", err)
		return
	}

	testResp.Duration = duration

	// Compare with baseline and create test result
	e.compareAndSaveResult(reqRecord, baselineResp, testResp, account)
}

// cloneRequest creates a copy of an HTTP request
func (e *Engine) cloneRequest(req *http.Request) (*http.Request, error) {
	// Read body
	var bodyBytes []byte
	if req.Body != nil {
		bodyBytes, _ = io.ReadAll(req.Body)
		req.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
	}

	// Create new request
	cloned, err := http.NewRequest(req.Method, req.URL.String(), bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, err
	}

	// Copy headers
	for key, values := range req.Header {
		for _, value := range values {
			cloned.Header.Add(key, value)
		}
	}

	return cloned, nil
}

// applyCredentials applies account credentials to a request
func (e *Engine) applyCredentials(req *http.Request, account config.Account) {
	// Apply headers
	for _, header := range account.Headers {
		req.Header.Set(header.Name, header.Value)
	}

	// Apply cookies
	for _, cookie := range account.Cookies {
		req.AddCookie(&http.Cookie{
			Name:  cookie.Name,
			Value: cookie.Value,
		})
	}

	// Apply parameters (for query parameters)
	if len(account.Params) > 0 {
		query := req.URL.Query()
		for _, param := range account.Params {
			query.Set(param.Name, param.Value)
		}
		req.URL.RawQuery = query.Encode()
	}
}

// compareAndSaveResult compares responses and saves the test result
func (e *Engine) compareAndSaveResult(reqRecord *storage.RequestRecord, baselineResp, testResp *storage.ResponseRecord, account config.Account) {
	// Calculate similarity
	similarityScore := similarity.CalculateStringSimilarity(baselineResp.Body, testResp.Body)

	// Determine if vulnerable
	profile, _ := e.db.GetActiveProfile()
	threshold := profile.Settings.SimilarityThreshold
	if threshold == 0 {
		threshold = 0.8 // Default threshold
	}

	isVulnerable := similarityScore > threshold && baselineResp.StatusCode == testResp.StatusCode

	// Determine vulnerability type
	vulnType := ""
	if isVulnerable {
		switch account.Type {
		case config.AccountTypeSame:
			vulnType = "horizontal"
		case config.AccountTypeLow:
			vulnType = "vertical"
		case config.AccountTypeCustom:
			vulnType = "custom"
		}
	}

	// Create test result
	result := &storage.TestResult{
		RequestID:        reqRecord.ID,
		BaselineResponse: baselineResp.ID,
		TestResponse:     testResp.ID,
		Similarity:       similarityScore,
		IsVulnerable:     isVulnerable,
		VulnType:         vulnType,
		Timestamp:        time.Now(),
	}

	if err := e.db.SaveTestResult(result); err != nil {
		logrus.Errorf("Failed to save test result: %v", err)
	}

	if isVulnerable {
		logrus.Warnf("Potential %s privilege escalation detected for %s with account %s (similarity: %.2f)",
			vulnType, reqRecord.URL, account.Name, similarityScore)
	}
}

// getSortedAccounts returns accounts sorted by priority (highest first)
func (e *Engine) getSortedAccounts(accounts []config.Account) []config.Account {
	sorted := make([]config.Account, len(accounts))
	copy(sorted, accounts)

	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i].Priority > sorted[j].Priority
	})

	return sorted
}

// createRequestHash creates a hash for request deduplication
func (e *Engine) createRequestHash(method, url, headers, body string) string {
	data := fmt.Sprintf("%s|%s|%s|%s", method, url, headers, body)
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)
}
