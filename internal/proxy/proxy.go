package proxy

import (
	"bufio"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strings"
	"time"

	"auth-tester/internal/replay"
	"auth-tester/internal/storage"

	"github.com/sirupsen/logrus"
)

// Server represents the proxy server
type Server struct {
	port        int
	db          *storage.DB
	replayEngine *replay.Engine
	listener    net.Listener
}

// NewServer creates a new proxy server
func NewServer(port int, db *storage.DB) *Server {
	return &Server{
		port:        port,
		db:          db,
		replayEngine: replay.NewEngine(db),
	}
}

// Start starts the proxy server
func (s *Server) Start() error {
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", s.port))
	if err != nil {
		return fmt.Errorf("failed to start proxy server: %w", err)
	}
	
	s.listener = listener
	logrus.Infof("Proxy server listening on port %d", s.port)

	for {
		conn, err := listener.Accept()
		if err != nil {
			logrus.Errorf("Failed to accept connection: %v", err)
			continue
		}

		go s.handleConnection(conn)
	}
}

// Stop stops the proxy server
func (s *Server) Stop() error {
	if s.listener != nil {
		return s.listener.Close()
	}
	return nil
}

// handleConnection handles a single proxy connection
func (s *Server) handleConnection(clientConn net.Conn) {
	defer clientConn.Close()

	reader := bufio.NewReader(clientConn)
	
	// Read the first line to determine if it's HTTP or HTTPS
	firstLine, err := reader.ReadLine()
	if err != nil {
		logrus.Errorf("Failed to read first line: %v", err)
		return
	}

	line := string(firstLine)
	
	if strings.HasPrefix(line, "CONNECT") {
		s.handleHTTPS(clientConn, line)
	} else {
		s.handleHTTP(clientConn, reader, line)
	}
}

// handleHTTPS handles HTTPS CONNECT requests
func (s *Server) handleHTTPS(clientConn net.Conn, connectLine string) {
	// Parse CONNECT request
	parts := strings.Split(connectLine, " ")
	if len(parts) < 2 {
		logrus.Error("Invalid CONNECT request")
		return
	}

	host := parts[1]
	
	// Connect to the target server
	serverConn, err := net.Dial("tcp", host)
	if err != nil {
		logrus.Errorf("Failed to connect to %s: %v", host, err)
		clientConn.Write([]byte("HTTP/1.1 502 Bad Gateway\r\n\r\n"))
		return
	}
	defer serverConn.Close()

	// Send 200 Connection Established
	clientConn.Write([]byte("HTTP/1.1 200 Connection Established\r\n\r\n"))

	// Start tunneling
	go io.Copy(serverConn, clientConn)
	io.Copy(clientConn, serverConn)
}

// handleHTTP handles HTTP requests
func (s *Server) handleHTTP(clientConn net.Conn, reader *bufio.Reader, firstLine string) {
	// Parse the request
	req, err := s.parseHTTPRequest(reader, firstLine)
	if err != nil {
		logrus.Errorf("Failed to parse HTTP request: %v", err)
		return
	}

	// Check if we should process this request
	if !s.shouldProcessRequest(req) {
		s.forwardRequest(clientConn, req)
		return
	}

	// Capture and process the request
	s.processRequest(clientConn, req)
}

// parseHTTPRequest parses an HTTP request from the reader
func (s *Server) parseHTTPRequest(reader *bufio.Reader, firstLine string) (*http.Request, error) {
	// Parse the request line
	parts := strings.Split(firstLine, " ")
	if len(parts) < 3 {
		return nil, fmt.Errorf("invalid request line: %s", firstLine)
	}

	method := parts[0]
	urlStr := parts[1]
	
	// Parse URL
	var reqURL *url.URL
	var err error
	
	if strings.HasPrefix(urlStr, "http://") || strings.HasPrefix(urlStr, "https://") {
		reqURL, err = url.Parse(urlStr)
	} else {
		reqURL, err = url.Parse("http://" + urlStr)
	}
	
	if err != nil {
		return nil, fmt.Errorf("invalid URL: %s", urlStr)
	}

	// Create request
	req := &http.Request{
		Method: method,
		URL:    reqURL,
		Header: make(http.Header),
	}

	// Read headers
	for {
		line, err := reader.ReadLine()
		if err != nil {
			return nil, err
		}
		
		lineStr := string(line)
		if lineStr == "" {
			break
		}

		parts := strings.SplitN(lineStr, ":", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			req.Header.Add(key, value)
		}
	}

	// Read body if present
	if contentLength := req.Header.Get("Content-Length"); contentLength != "" {
		// For simplicity, we'll handle this in the actual implementation
		// This is a basic structure
	}

	return req, nil
}

// shouldProcessRequest determines if a request should be processed for auth testing
func (s *Server) shouldProcessRequest(req *http.Request) bool {
	// Get active profile
	profile, err := s.db.GetActiveProfile()
	if err != nil || profile == nil {
		return false
	}

	// Check if URL matches target scope
	for _, scope := range profile.TargetScope {
		if strings.Contains(req.URL.String(), scope) {
			// Check blacklist
			for _, blocked := range profile.Blacklist {
				if strings.Contains(req.URL.String(), blocked) {
					return false
				}
			}
			return true
		}
	}

	return false
}

// processRequest processes a request for authentication testing
func (s *Server) processRequest(clientConn net.Conn, req *http.Request) {
	// Forward the original request and get response
	resp, err := s.forwardRequestAndGetResponse(req)
	if err != nil {
		logrus.Errorf("Failed to forward request: %v", err)
		return
	}

	// Send response back to client
	s.writeResponse(clientConn, resp)

	// Trigger replay testing in background
	go s.replayEngine.ProcessRequest(req, resp)
}

// forwardRequest forwards a request without processing
func (s *Server) forwardRequest(clientConn net.Conn, req *http.Request) {
	resp, err := s.forwardRequestAndGetResponse(req)
	if err != nil {
		logrus.Errorf("Failed to forward request: %v", err)
		return
	}

	s.writeResponse(clientConn, resp)
}

// forwardRequestAndGetResponse forwards a request and returns the response
func (s *Server) forwardRequestAndGetResponse(req *http.Request) (*http.Response, error) {
	client := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	return client.Do(req)
}

// writeResponse writes an HTTP response to the connection
func (s *Server) writeResponse(conn net.Conn, resp *http.Response) {
	defer resp.Body.Close()
	
	// Write status line
	fmt.Fprintf(conn, "HTTP/1.1 %s\r\n", resp.Status)
	
	// Write headers
	for key, values := range resp.Header {
		for _, value := range values {
			fmt.Fprintf(conn, "%s: %s\r\n", key, value)
		}
	}
	
	// Write empty line
	fmt.Fprintf(conn, "\r\n")
	
	// Write body
	io.Copy(conn, resp.Body)
}
